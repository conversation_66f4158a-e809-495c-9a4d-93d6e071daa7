<?php 
session_start();
error_reporting(0);
include 'include/config.php';
$uid=$_SESSION['uid'];

if(isset($_POST['submit']))
{ 
$pid=$_POST['pid'];

$sql="INSERT INTO tblbooking (package_id,userid) Values(:pid,:uid)";

$query = $dbh -> prepare($sql);
$query->bindParam(':pid',$pid,PDO::PARAM_STR);
$query->bindParam(':uid',$uid,PDO::PARAM_STR);
$query -> execute();
echo "<script>alert('Package has been booked.');</script>";
echo "<script>window.location.href='booking-history.php'</script>";

}
?>
<!DOCTYPE html>
<html lang="en">
<head>
	<title>GYM Management System - Transform Your Fitness Journey</title>
	<meta charset="UTF-8">
	<meta name="description" content="Premium Gym Management System - Your path to fitness excellence">
	<meta name="keywords" content="gym, fitness, workout, training, health">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<!-- Stylesheets -->
	<link rel="stylesheet" href="css/bootstrap.min.css"/>
	<link rel="stylesheet" href="css/font-awesome.min.css"/>
	<link rel="stylesheet" href="css/owl.carousel.min.css"/>
	<link rel="stylesheet" href="css/nice-select.css"/>
	<link rel="stylesheet" href="css/magnific-popup.css"/>
	<link rel="stylesheet" href="css/slicknav.min.css"/>
	<link rel="stylesheet" href="css/animate.css"/>
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	<!-- Main Stylesheets -->
	<link rel="stylesheet" href="css/style.css"/>
	<style>
		/* Modern Custom Styles */
		:root {
			--primary-color: #667eea;
			--secondary-color: #764ba2;
			--accent-color: #f093fb;
			--dark-color: #2d3748;
			--light-color: #f7fafc;
			--success-color: #48bb78;
			--warning-color: #ed8936;
			--danger-color: #f56565;
		}
		
		body {
			font-family: 'Poppins', sans-serif;
			line-height: 1.6;
		}
		
		/* Hero Section */
		.hero-section {
			background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
			min-height: 100vh;
			display: flex;
			align-items: center;
			position: relative;
			overflow: hidden;
		}
		
		.hero-section::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('img/page-top-bg.jpg') center/cover;
			opacity: 0.1;
			z-index: 1;
		}
		
		.hero-content {
			position: relative;
			z-index: 2;
			text-align: center;
			color: white;
		}
		
		.hero-content h1 {
			font-size: 4rem;
			font-weight: 700;
			margin-bottom: 1.5rem;
			text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
		}
		
		.hero-content p {
			font-size: 1.25rem;
			margin-bottom: 2rem;
			opacity: 0.9;
		}
		
		.hero-buttons {
			display: flex;
			gap: 1rem;
			justify-content: center;
			flex-wrap: wrap;
		}
		
		.btn-hero {
			padding: 15px 30px;
			border-radius: 50px;
			font-weight: 600;
			text-transform: uppercase;
			letter-spacing: 1px;
			transition: all 0.3s ease;
			border: none;
			cursor: pointer;
		}
		
		.btn-primary-hero {
			background: linear-gradient(45deg, var(--accent-color), var(--success-color));
			color: white;
			box-shadow: 0 10px 30px rgba(0,0,0,0.2);
		}
		
		.btn-primary-hero:hover {
			transform: translateY(-3px);
			box-shadow: 0 15px 40px rgba(0,0,0,0.3);
			color: white;
		}
		
		.btn-outline-hero {
			background: transparent;
			color: white;
			border: 2px solid white;
		}
		
		.btn-outline-hero:hover {
			background: white;
			color: var(--primary-color);
			transform: translateY(-3px);
		}
		
		/* Features Section */
		.features-section {
			padding: 80px 0;
			background: var(--light-color);
		}
		
		.feature-card {
			background: white;
			padding: 2rem;
			border-radius: 15px;
			text-align: center;
			box-shadow: 0 5px 20px rgba(0,0,0,0.1);
			transition: all 0.3s ease;
			height: 100%;
		}
		
		.feature-card:hover {
			transform: translateY(-10px);
			box-shadow: 0 15px 40px rgba(0,0,0,0.15);
		}
		
		.feature-icon {
			width: 80px;
			height: 80px;
			background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 1.5rem;
			color: white;
			font-size: 2rem;
		}
		
		/* Pricing Section Redesign */
		.pricing-section {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			padding: 100px 0;
			position: relative;
		}
		
		.pricing-section::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('img/page-top-bg.jpg') center/cover;
			opacity: 0.05;
		}
		
		.section-title {
			position: relative;
			z-index: 2;
		}
		
		.section-title h2 {
			color: white;
			font-size: 3rem;
			font-weight: 700;
			margin-bottom: 1rem;
		}
		
		.section-title p {
			color: rgba(255,255,255,0.9);
			font-size: 1.1rem;
		}
		
		.pricing-item {
			background: white;
			border-radius: 20px;
			padding: 2.5rem;
			text-align: center;
			box-shadow: 0 10px 40px rgba(0,0,0,0.1);
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;
			height: 100%;
		}
		
		.pricing-item::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 5px;
			background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
		}
		
		.pricing-item:hover {
			transform: translateY(-10px);
			box-shadow: 0 20px 60px rgba(0,0,0,0.15);
		}
		
		.pricing-item .pi-top h4 {
			font-size: 1.5rem;
			font-weight: 600;
			color: var(--dark-color);
			margin-bottom: 1rem;
		}
		
		.pricing-item .pi-price {
			margin: 2rem 0;
		}
		
		.pricing-item .pi-price h3 {
			font-size: 3rem;
			font-weight: 700;
			color: var(--primary-color);
			margin-bottom: 0.5rem;
		}
		
		.pricing-item .pi-price p {
			color: #666;
			font-size: 1rem;
		}
		
		.pricing-item ul {
			list-style: none;
			padding: 0;
			margin: 2rem 0;
		}
		
		.pricing-item ul li {
			padding: 0.5rem 0;
			color: #666;
			border-bottom: 1px solid #eee;
		}
		
		.pricing-item ul li:last-child {
			border-bottom: none;
		}
		
		.site-btn.sb-line-gradient {
			background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
			color: white;
			border: none;
			padding: 15px 30px;
			border-radius: 50px;
			font-weight: 600;
			text-transform: uppercase;
			letter-spacing: 1px;
			transition: all 0.3s ease;
			width: 100%;
			margin-top: 1rem;
		}
		
		.site-btn.sb-line-gradient:hover {
			transform: translateY(-2px);
			box-shadow: 0 10px 30px rgba(0,0,0,0.2);
			color: white;
		}
		
		/* Responsive Design */
		@media (max-width: 768px) {
			.hero-content h1 {
				font-size: 2.5rem;
			}
			
			.hero-buttons {
				flex-direction: column;
				align-items: center;
			}
			
			.section-title h2 {
				font-size: 2rem;
			}
			
			.pricing-item {
				margin-bottom: 2rem;
			}
		}
		
		/* Footer Styling */
		.footer-section {
			background: linear-gradient(135deg, var(--dark-color) 0%, #1a202c 100%);
			color: white;
			padding: 60px 0 20px;
		}
		
		.footer-widget {
			margin-bottom: 30px;
		}
		
		.footer-widget .fw-title h3 {
			color: white;
			font-size: 1.5rem;
			font-weight: 600;
			margin-bottom: 1.5rem;
			position: relative;
		}
		
		.footer-widget .fw-title h3::after {
			content: '';
			position: absolute;
			bottom: -8px;
			left: 0;
			width: 40px;
			height: 3px;
			background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
			border-radius: 2px;
		}
		
		.footer-widget p {
			color: rgba(255,255,255,0.8);
			line-height: 1.8;
		}
		
		.footer-widget ul {
			list-style: none;
			padding: 0;
		}
		
		.footer-widget ul li {
			margin-bottom: 12px;
			color: rgba(255,255,255,0.8);
		}
		
		.footer-widget ul li i {
			margin-right: 10px;
			color: var(--primary-color);
		}
		
		.footer-widget ul li a {
			color: rgba(255,255,255,0.8);
			transition: all 0.3s ease;
		}
		
		.footer-widget ul li a:hover {
			color: var(--primary-color);
			text-decoration: none;
		}
		
		.footer-social {
			margin-top: 20px;
		}
		
		.footer-social a {
			display: inline-block;
			width: 40px;
			height: 40px;
			background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
			color: white;
			text-align: center;
			line-height: 40px;
			border-radius: 50%;
			margin-right: 10px;
			transition: all 0.3s ease;
		}
		
		.footer-social a:hover {
			transform: translateY(-3px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.3);
			color: white;
		}
		
		.footer-bottom {
			border-top: 1px solid rgba(255,255,255,0.1);
			padding-top: 20px;
			margin-top: 40px;
		}
		
		.footer-bottom .copyright p {
			color: rgba(255,255,255,0.7);
			margin: 0;
		}
		
		.footer-bottom-links a {
			color: rgba(255,255,255,0.7);
			margin-left: 20px;
			transition: all 0.3s ease;
		}
		
		.footer-bottom-links a:hover {
			color: var(--primary-color);
			text-decoration: none;
		}
		
		/* Header Enhancements */
		.header-section {
			background: rgba(255,255,255,0.95);
			backdrop-filter: blur(10px);
			box-shadow: 0 2px 20px rgba(0,0,0,0.1);
		}
		
		.header-info a {
			color: var(--dark-color);
			transition: all 0.3s ease;
		}
		
		.header-info a:hover {
			color: var(--primary-color);
			text-decoration: none;
		}
		
		.main-menu li a {
			color: var(--dark-color);
			font-weight: 500;
			transition: all 0.3s ease;
		}
		
		.main-menu li a:hover,
		.main-menu li a.active {
			color: var(--primary-color);
		}
		
		/* Animation Classes */
		.fade-in {
			animation: fadeIn 1s ease-in;
		}
		
		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(30px); }
			to { opacity: 1; transform: translateY(0); }
		}
		
		.slide-up {
			animation: slideUp 0.8s ease-out;
		}
		
		@keyframes slideUp {
			from { opacity: 0; transform: translateY(50px); }
			to { opacity: 1; transform: translateY(0); }
		}
	</style>
</head>
<body>
	<!-- Header Section -->
	<?php include 'include/header.php';?>
	<!-- Header Section end -->

	<!-- Hero Section -->
	<section class="hero-section">
		<div class="container">
			<div class="row">
				<div class="col-lg-12">
					<div class="hero-content fade-in">
						<h1>Transform Your Fitness Journey</h1>
						<p>Join our premium gym management system and take your fitness to the next level with personalized training programs, expert guidance, and state-of-the-art facilities.</p>
						<div class="hero-buttons">
							<a href="#pricing" class="btn-hero btn-primary-hero">Get Started Today</a>
							<a href="about.php" class="btn-hero btn-outline-hero">Learn More</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Features Section -->
	<section class="features-section">
		<div class="container">
			<div class="row">
				<div class="col-lg-4 col-md-6 mb-4">
					<div class="feature-card slide-up">
						<div class="feature-icon">
							<i class="fa fa-dumbbell"></i>
						</div>
						<h4>Premium Equipment</h4>
						<p>Access to state-of-the-art fitness equipment and facilities designed for optimal performance.</p>
					</div>
				</div>
				<div class="col-lg-4 col-md-6 mb-4">
					<div class="feature-card slide-up">
						<div class="feature-icon">
							<i class="fa fa-users"></i>
						</div>
						<h4>Expert Trainers</h4>
						<p>Certified personal trainers dedicated to helping you achieve your fitness goals.</p>
					</div>
				</div>
				<div class="col-lg-4 col-md-6 mb-4">
					<div class="feature-card slide-up">
						<div class="feature-icon">
							<i class="fa fa-calendar"></i>
						</div>
						<h4>Flexible Scheduling</h4>
						<p>24/7 access with flexible booking system to fit your busy lifestyle.</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Pricing Section -->
	<section class="pricing-section" id="pricing">
		<div class="container">
			<div class="section-title text-center">
				<h2>Choose Your Plan</h2>
				<p>Select the perfect membership plan that fits your fitness goals and lifestyle</p>
			</div>
			<div class="row">
				<?php 
				$sql ="SELECT id, category, titlename, PackageType, PackageDuratiobn, Price, uploadphoto, Description, create_date from tbladdpackage";
				$query= $dbh -> prepare($sql);
				$query-> execute();
				$results = $query -> fetchAll(PDO::FETCH_OBJ);
				$cnt=1;
				if($query -> rowCount() > 0)
				{
				foreach($results as $result)
				{
				?>
				<div class="col-lg-3 col-md-6 mb-4">
					<div class="pricing-item slide-up">
						<div class="pi-top">
							<h4><?php echo $result->titlename;?></h4>
						</div>
						<div class="pi-price">
							<h3>$<?php echo htmlentities($result->Price);?></h3>
							<p><?php echo $result->PackageDuratiobn;?></p>
						</div>
						<ul>
							<?php echo $result->Description;?>
						</ul>
						<?php if(strlen($_SESSION['uid'])==0): ?>
						<a href="login.php" class="site-btn sb-line-gradient">Book Now</a>
						<?php else :?>
						<form method='post'>
							<input type='hidden' name='pid' value='<?php echo htmlentities($result->id);?>'>
							<input class='site-btn sb-line-gradient' type='submit' name='submit' value='Book Now' onclick="return confirm('Do you really want to book this package?');"> 
						</form> 
						<?php endif;?>
					</div>
				</div>
				<?php  $cnt=$cnt+1; } } ?>
			</div>
		</div>
	</section>

	<!-- Footer Section -->
	<?php include 'include/footer.php'; ?>
	<!-- Footer Section end -->

	<div class="back-to-top"><img src="img/icons/up-arrow.png" alt=""></div>

	<!--====== Javascripts & Jquery ======-->
	<script src="js/vendor/jquery-3.2.1.min.js"></script>
	<script src="js/bootstrap.min.js"></script>
	<script src="js/jquery.slicknav.min.js"></script>
	<script src="js/owl.carousel.min.js"></script>
	<script src="js/jquery.nice-select.min.js"></script>
	<script src="js/jquery-ui.min.js"></script>
	<script src="js/jquery.magnific-popup.min.js"></script>
	<script src="js/main.js"></script>
	
	<script>
		// Smooth scrolling for anchor links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				e.preventDefault();
				document.querySelector(this.getAttribute('href')).scrollIntoView({
					behavior: 'smooth'
				});
			});
		});
		
		// Add animation on scroll
		window.addEventListener('scroll', function() {
			const elements = document.querySelectorAll('.slide-up');
			elements.forEach(element => {
				const position = element.getBoundingClientRect();
				if(position.top < window.innerHeight && position.bottom >= 0) {
					element.style.opacity = '1';
					element.style.transform = 'translateY(0)';
				}
			});
		});
	</script>

</body>
</html>
