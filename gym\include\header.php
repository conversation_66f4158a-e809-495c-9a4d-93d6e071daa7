	<header class="header-section">
		<div class="header-top">
			<div class="row m-0">
				<div class="col-md-6 d-none d-md-block p-0">
					<div class="header-info">
						<i class="material-icons">location_on</i>
						<p>Premium Fitness Center</p>
					</div>
					<div class="header-info">
						<i class="material-icons">phone</i>
						<p>+****************</p>
					</div>
				</div>
				<div class="col-md-6 text-left text-md-right p-0">
                 <?php if(strlen($_SESSION['uid'])==0): ?>
					<div class="header-info d-none d-md-inline-flex">
						<i class="material-icons">account_circle</i>
						<a href="login.php"><p>Login</p></a>
					</div>
					<div class="header-info d-none d-md-inline-flex">
						<i class="material-icons">person_add</i>
						<a href="registration.php"><p>Register</p></a>
					</div>
					<?php else :?>
					<div class="header-info d-none d-md-inline-flex">
						<i class="material-icons">account_circle</i>
						<a href="profile.php"><p>My Profile</p></a>
					</div>
					<div class="header-info d-none d-md-inline-flex">
						<i class="material-icons">lock</i>
						<a href="changepassword.php"><p>Change Password</p></a>
					</div>
					<div class="header-info d-none d-md-inline-flex">
						<i class="material-icons">logout</i>
						<a href="logout.php"><p>Logout</p></a>
					</div>
					
					<?php endif;?>
				</div>
			</div>
		</div>
		<div class="header-bottom">
			<a href="index.php" class="site-logo">
				<span style="color:#667eea; font-weight:700; font-size:28px;">GYM</span>
				<span style="color:#764ba2; font-weight:600; font-size:28px;">PRO</span>
				<br />
				<small style="color:#666; font-size:12px; margin-top:-4%;">Premium Fitness Management</small>
			</a>
			
			<div class="container">
				<ul class="main-menu">
					<li><a href="index.php" class="active">Home</a></li>
					<li><a href="about.php">About</a></li>
					<li><a href="contact.php">Contact</a></li>
					
					<?php if(strlen($_SESSION['uid'])==0): ?>
			<li><a href="admin/">Admin</a></li>
					<?php else :?>
						<li><a href="booking-history.php">My Bookings</a></li>
						<?php endif;?>
				</ul>
			</div>
		</div>
	</header>